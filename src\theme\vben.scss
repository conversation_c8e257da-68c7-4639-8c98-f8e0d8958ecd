// Vben Admin 风格样式

// 全局样式重置
* {
	box-sizing: border-box;
}

html {
	font-size: 14px;
	line-height: $line-height-normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	margin: 0;
	padding: 0;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
	font-size: $font-size-sm;
	color: $text-color-primary;
	background-color: $bg-color-secondary;
	transition: background-color $transition-base;
}

// 卡片样式
.vben-card {
	background: $bg-color-primary;
	border-radius: $border-radius-large;
	box-shadow: $shadow-light;
	border: 1px solid $border-color-tertiary;
	transition: all $transition-base;

	&:hover {
		box-shadow: $shadow-medium;
	}

	.card-header {
		padding: $spacing-lg $spacing-xl;
		border-bottom: 1px solid $border-color-tertiary;

		.card-title {
			margin: 0;
			font-size: $font-size-lg;
			font-weight: $font-weight-medium;
			color: $text-color-primary;
		}
	}

	.card-body {
		padding: $spacing-xl;
	}

	.card-footer {
		padding: $spacing-lg $spacing-xl;
		border-top: 1px solid $border-color-tertiary;
		background-color: $bg-color-secondary;
		border-radius: 0 0 $border-radius-large $border-radius-large;
	}
}

// 按钮样式增强
.vben-button {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: $spacing-sm $spacing-lg;
	border-radius: $border-radius-base;
	font-size: $font-size-sm;
	font-weight: $font-weight-medium;
	line-height: $line-height-tight;
	text-decoration: none;
	border: 1px solid transparent;
	cursor: pointer;
	transition: all $transition-fast;
	user-select: none;

	&:focus {
		outline: none;
		box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
	}

	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	// 主要按钮
	&.primary {
		background-color: $primary-color;
		border-color: $primary-color;
		color: white;

		&:hover:not(:disabled) {
			background-color: $primary-color-hover;
			border-color: $primary-color-hover;
		}

		&:active {
			background-color: $primary-color-active;
			border-color: $primary-color-active;
		}
	}

	// 次要按钮
	&.secondary {
		background-color: transparent;
		border-color: $border-color-primary;
		color: $text-color-primary;

		&:hover:not(:disabled) {
			border-color: $primary-color;
			color: $primary-color;
		}
	}

	// 成功按钮
	&.success {
		background-color: $success-color;
		border-color: $success-color;
		color: white;

		&:hover:not(:disabled) {
			background-color: $success-color-hover;
			border-color: $success-color-hover;
		}
	}

	// 警告按钮
	&.warning {
		background-color: $warning-color;
		border-color: $warning-color;
		color: white;

		&:hover:not(:disabled) {
			background-color: $warning-color-hover;
			border-color: $warning-color-hover;
		}
	}

	// 危险按钮
	&.danger {
		background-color: $error-color;
		border-color: $error-color;
		color: white;

		&:hover:not(:disabled) {
			background-color: $error-color-hover;
			border-color: $error-color-hover;
		}
	}

	// 按钮尺寸
	&.small {
		padding: $spacing-xs $spacing-md;
		font-size: $font-size-xs;
	}

	&.large {
		padding: $spacing-md $spacing-xl;
		font-size: $font-size-base;
	}
}

// 表单样式增强
.vben-form {
	.form-item {
		margin-bottom: $spacing-lg;

		.form-label {
			display: block;
			margin-bottom: $spacing-xs;
			font-size: $font-size-sm;
			font-weight: $font-weight-medium;
			color: $text-color-primary;
		}

		.form-control {
			width: 100%;
			padding: $spacing-sm $spacing-md;
			border: 1px solid $border-color-primary;
			border-radius: $border-radius-base;
			font-size: $font-size-sm;
			color: $text-color-primary;
			background-color: $bg-color-primary;
			transition: all $transition-fast;

			&:focus {
				outline: none;
				border-color: $primary-color;
				box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
			}

			&:disabled {
				background-color: $bg-color-tertiary;
				color: $text-color-tertiary;
				cursor: not-allowed;
			}

			&.error {
				border-color: $error-color;

				&:focus {
					box-shadow: 0 0 0 2px rgba($error-color, 0.2);
				}
			}
		}

		.form-error {
			margin-top: $spacing-xs;
			font-size: $font-size-xs;
			color: $error-color;
		}

		.form-help {
			margin-top: $spacing-xs;
			font-size: $font-size-xs;
			color: $text-color-tertiary;
		}
	}
}

// 表格样式增强
.vben-table {
	width: 100%;
	background-color: $bg-color-primary;
	border-radius: $border-radius-large;
	overflow: hidden;
	box-shadow: $shadow-light;

	.table-header {
		background-color: $bg-color-secondary;

		th {
			padding: $spacing-lg;
			font-size: $font-size-sm;
			font-weight: $font-weight-semibold;
			color: $text-color-primary;
			text-align: left;
			border-bottom: 1px solid $border-color-secondary;
		}
	}

	.table-body {
		tr {
			transition: background-color $transition-fast;

			&:hover {
				background-color: $bg-color-secondary;
			}

			&:not(:last-child) {
				border-bottom: 1px solid $border-color-tertiary;
			}
		}

		td {
			padding: $spacing-lg;
			font-size: $font-size-sm;
			color: $text-color-primary;
		}
	}
}

// 导航样式
.vben-nav {
	.nav-item {
		display: flex;
		align-items: center;
		padding: $spacing-md $spacing-lg;
		color: $text-color-secondary;
		text-decoration: none;
		border-radius: $border-radius-base;
		transition: all $transition-fast;

		&:hover {
			background-color: $bg-color-tertiary;
			color: $text-color-primary;
		}

		&.active {
			background-color: $primary-color-light;
			color: $primary-color;
			font-weight: $font-weight-medium;
		}

		.nav-icon {
			margin-right: $spacing-sm;
			font-size: $font-size-base;
		}
	}
}

// 标签页样式
.vben-tabs {
	.tabs-nav {
		display: flex;
		border-bottom: 1px solid $border-color-secondary;

		.tab-item {
			padding: $spacing-md $spacing-lg;
			color: $text-color-secondary;
			cursor: pointer;
			border-bottom: 2px solid transparent;
			transition: all $transition-fast;

			&:hover {
				color: $text-color-primary;
			}

			&.active {
				color: $primary-color;
				border-bottom-color: $primary-color;
			}
		}
	}

	.tabs-content {
		padding: $spacing-xl 0;
	}
}

// 布局组件样式
.vben-header {
	background: $bg-color-primary !important;
	border-bottom: 1px solid $border-color-tertiary !important;
	box-shadow: $shadow-light !important;
	height: $header-height !important;
	padding: 0 $spacing-xl !important;
	display: flex !important;
	align-items: center !important;
	z-index: $z-index-sticky !important;
}

.layout-aside {
	background: $bg-color-primary !important;
	border-right: 1px solid $border-color-tertiary !important;
	box-shadow: $shadow-light !important;

	.el-menu {
		border-right: none !important;
		background-color: transparent !important;
	}

	.el-menu-item,
	.el-sub-menu__title {
		border-radius: $border-radius-base !important;
		margin: $spacing-xs $spacing-sm !important;
		transition: all $transition-fast !important;

		&:hover {
			background-color: $bg-color-secondary !important;
			color: $primary-color !important;
		}

		&.is-active {
			background-color: $primary-color-light !important;
			color: $primary-color !important;
			font-weight: $font-weight-medium !important;
		}
	}
}

.layout-main {
	background: $bg-color-secondary !important;
	padding: $spacing-lg !important;

	.layout-main-scroll {
		.el-scrollbar__view {
			padding: 0 !important;
		}
	}
}

// 面包屑样式
.layout-navbars-breadcrumb-index {
	display: flex !important;
	align-items: center !important;
	justify-content: space-between !important;
	width: 100% !important;
	height: 100% !important;

	.el-breadcrumb {
		.el-breadcrumb__item {
			.el-breadcrumb__inner {
				color: $text-color-secondary !important;
				font-weight: $font-weight-normal !important;

				&:hover {
					color: $primary-color !important;
				}

				&.is-link {
					color: $text-color-primary !important;
				}
			}

			&:last-child {
				.el-breadcrumb__inner {
					color: $text-color-primary !important;
					font-weight: $font-weight-medium !important;
				}
			}
		}
	}
}

// 标签页样式
.layout-navbars-tagsview {
	background: $bg-color-primary !important;
	border-bottom: 1px solid $border-color-tertiary !important;
	padding: $spacing-sm $spacing-lg !important;

	.tags-view-item {
		background: $bg-color-secondary !important;
		border: 1px solid $border-color-secondary !important;
		border-radius: $border-radius-base !important;
		color: $text-color-secondary !important;
		padding: $spacing-xs $spacing-md !important;
		margin-right: $spacing-sm !important;
		font-size: $font-size-xs !important;
		transition: all $transition-fast !important;

		&:hover {
			background: $bg-color-tertiary !important;
			color: $text-color-primary !important;
		}

		&.is-active {
			background: $primary-color !important;
			border-color: $primary-color !important;
			color: white !important;
		}
	}
}

// Logo 样式
.layout-logo {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	padding: $spacing-lg !important;
	border-bottom: 1px solid $border-color-tertiary !important;

	.layout-logo-medium-img {
		height: 32px !important;
		width: auto !important;
	}
}

// 用户信息样式
.layout-navbars-breadcrumb-user {
	display: flex !important;
	align-items: center !important;

	.el-dropdown-link {
		display: flex !important;
		align-items: center !important;
		color: $text-color-primary !important;
		cursor: pointer !important;
		padding: $spacing-sm !important;
		border-radius: $border-radius-base !important;
		transition: all $transition-fast !important;

		&:hover {
			background-color: $bg-color-secondary !important;
		}

		.el-avatar {
			margin-right: $spacing-sm !important;
		}
	}
}

// 响应式工具类
@media (max-width: $breakpoint-md) {
	.vben-card {
		.card-header,
		.card-body,
		.card-footer {
			padding: $spacing-md;
		}
	}

	.vben-table {
		.table-header th,
		.table-body td {
			padding: $spacing-md;
		}
	}

	.vben-header {
		padding: 0 $spacing-md !important;
	}

	.layout-main {
		padding: $spacing-md !important;
	}
}
