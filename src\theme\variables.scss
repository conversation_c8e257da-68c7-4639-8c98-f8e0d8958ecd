// Vben Admin 风格变量定义

// 主色调
$primary-color: #1890ff;
$primary-color-hover: #40a9ff;
$primary-color-active: #096dd9;
$primary-color-light: #e6f7ff;

// 成功色
$success-color: #52c41a;
$success-color-hover: #73d13d;
$success-color-active: #389e0d;
$success-color-light: #f6ffed;

// 警告色
$warning-color: #faad14;
$warning-color-hover: #ffc53d;
$warning-color-active: #d48806;
$warning-color-light: #fffbe6;

// 错误色
$error-color: #ff4d4f;
$error-color-hover: #ff7875;
$error-color-active: #d9363e;
$error-color-light: #fff2f0;

// 信息色
$info-color: #1890ff;
$info-color-hover: #40a9ff;
$info-color-active: #096dd9;
$info-color-light: #e6f7ff;

// 中性色
$text-color-primary: #262626;
$text-color-secondary: #595959;
$text-color-tertiary: #8c8c8c;
$text-color-quaternary: #bfbfbf;

// 背景色
$bg-color-primary: #ffffff;
$bg-color-secondary: #fafafa;
$bg-color-tertiary: #f5f5f5;
$bg-color-quaternary: #f0f0f0;

// 边框色
$border-color-primary: #d9d9d9;
$border-color-secondary: #e8e8e8;
$border-color-tertiary: #f0f0f0;

// 阴影
$shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// 圆角
$border-radius-small: 2px;
$border-radius-base: 4px;
$border-radius-large: 6px;
$border-radius-xl: 8px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-2xl: 24px;
$spacing-3xl: 32px;
$spacing-4xl: 40px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;

// 行高
$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 布局尺寸
$header-height: 64px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 48px;

// 过渡动画
$transition-fast: 0.15s ease-in-out;
$transition-base: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1600px;
