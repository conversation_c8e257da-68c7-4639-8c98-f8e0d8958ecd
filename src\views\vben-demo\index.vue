<template>
  <div class="vben-demo-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">Vben Admin 风格演示</h1>
      <p class="page-description">展示重构后的UI组件和设计风格</p>
    </div>

    <!-- 卡片网格 -->
    <el-row :gutter="24" class="demo-grid">
      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in stats" :key="index">
        <div class="vben-card stat-card">
          <div class="card-body">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
              <el-icon :size="14">
                <ArrowUp v-if="stat.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 表单和表格演示 -->
    <el-row :gutter="24" class="demo-content">
      <!-- 表单演示 -->
      <el-col :xs="24" :lg="12">
        <div class="vben-card">
          <div class="card-header">
            <h3 class="card-title">表单演示</h3>
          </div>
          <div class="card-body">
            <el-form :model="form" label-width="80px" class="vben-form">
              <el-form-item label="用户名">
                <el-input v-model="form.username" placeholder="请输入用户名" />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="form.email" type="email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="角色">
                <el-select v-model="form.role" placeholder="请选择角色" style="width: 100%">
                  <el-option label="管理员" value="admin" />
                  <el-option label="用户" value="user" />
                  <el-option label="访客" value="guest" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-switch v-model="form.status" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" class="vben-button primary">提交</el-button>
                <el-button class="vben-button secondary">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-col>

      <!-- 按钮演示 -->
      <el-col :xs="24" :lg="12">
        <div class="vben-card">
          <div class="card-header">
            <h3 class="card-title">按钮演示</h3>
          </div>
          <div class="card-body">
            <div class="button-group">
              <h4>基础按钮</h4>
              <div class="button-row">
                <el-button type="primary">主要按钮</el-button>
                <el-button>默认按钮</el-button>
                <el-button type="success">成功按钮</el-button>
                <el-button type="warning">警告按钮</el-button>
                <el-button type="danger">危险按钮</el-button>
              </div>
            </div>
            
            <div class="button-group">
              <h4>按钮尺寸</h4>
              <div class="button-row">
                <el-button type="primary" size="large">大型按钮</el-button>
                <el-button type="primary">默认按钮</el-button>
                <el-button type="primary" size="small">小型按钮</el-button>
              </div>
            </div>

            <div class="button-group">
              <h4>图标按钮</h4>
              <div class="button-row">
                <el-button type="primary" :icon="Search">搜索</el-button>
                <el-button type="success" :icon="Plus">新增</el-button>
                <el-button type="warning" :icon="Edit">编辑</el-button>
                <el-button type="danger" :icon="Delete">删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 表格演示 -->
    <div class="vben-card">
      <div class="card-header">
        <h3 class="card-title">表格演示</h3>
      </div>
      <div class="card-body">
        <el-table :data="tableData" class="vben-table" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="role" label="角色" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="scope.row.status === '活跃' ? 'success' : 'info'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="primary" size="small" :icon="Edit">编辑</el-button>
              <el-button type="danger" size="small" :icon="Delete">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 标签页演示 -->
    <div class="vben-card">
      <div class="card-header">
        <h3 class="card-title">标签页演示</h3>
      </div>
      <div class="card-body">
        <el-tabs v-model="activeTab" class="vben-tabs">
          <el-tab-pane label="用户管理" name="users">
            <p>用户管理内容区域</p>
          </el-tab-pane>
          <el-tab-pane label="角色管理" name="roles">
            <p>角色管理内容区域</p>
          </el-tab-pane>
          <el-tab-pane label="权限管理" name="permissions">
            <p>权限管理内容区域</p>
          </el-tab-pane>
          <el-tab-pane label="系统设置" name="settings">
            <p>系统设置内容区域</p>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="VbenDemo">
import { ref, reactive } from 'vue'
import { 
  User, 
  Document, 
  Setting, 
  Monitor,
  ArrowUp,
  ArrowDown,
  Search,
  Plus,
  Edit,
  Delete
} from '@element-plus/icons-vue'

// 统计数据
const stats = ref([
  {
    label: '总用户数',
    value: '2,847',
    icon: User,
    color: '#1890ff',
    trend: 12.5
  },
  {
    label: '总订单数',
    value: '1,234',
    icon: Document,
    color: '#52c41a',
    trend: 8.2
  },
  {
    label: '系统监控',
    value: '98.5%',
    icon: Monitor,
    color: '#faad14',
    trend: -2.1
  },
  {
    label: '配置项',
    value: '156',
    icon: Setting,
    color: '#f5222d',
    trend: 5.7
  }
])

// 表单数据
const form = reactive({
  username: '',
  email: '',
  role: '',
  status: true
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    role: '管理员',
    status: '活跃'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    role: '用户',
    status: '活跃'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    role: '访客',
    status: '禁用'
  },
  {
    id: 4,
    name: '赵六',
    email: '<EMAIL>',
    role: '用户',
    status: '活跃'
  }
])

// 活跃标签页
const activeTab = ref('users')
</script>

<style lang="scss" scoped>
.vben-demo-container {
  padding: 24px;
  background: var(--next-bg-main-color);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
  
  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }
  
  .page-description {
    font-size: 16px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.demo-grid {
  margin-bottom: 24px;
}

.demo-content {
  margin-bottom: 24px;
}

.stat-card {
  .card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .stat-content {
    display: flex;
    align-items: center;
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      color: white;
    }
    
    .stat-info {
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        line-height: 1;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .stat-trend {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    
    &.positive {
      color: #52c41a;
    }
    
    &.negative {
      color: #f5222d;
    }
    
    .el-icon {
      margin-right: 2px;
    }
  }
}

.button-group {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin: 0 0 12px 0;
  }
  
  .button-row {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}
</style>
